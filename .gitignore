# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Flask stuff
instance/
.webassets-cache

# Environments
.env
.venv
ENV/
venv/
/.remember/
# Docker
*.log
docker-compose.override.yml

# VS Code
.vscode/

# Mac
.DS_Store

# Windows
Thumbs.db 

#log and certificates for ssl
ssl/
logs/
*.log
*.log.*
*.log.*.*
*.log.*.*.*
*.log.*.*.*.*
*.log.*.*.*.*.*
*.log.*.*.*.*.*.*
*.log.*.*.*.*.*.*.*
*.log.*.*.*.*.*.*.*.*